{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\mobile\\flutter\\Buoi13\\bttl\\android\\app\\.cxx\\Debug\\1z33661n\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\mobile\\flutter\\Buoi13\\bttl\\android\\app\\.cxx\\Debug\\1z33661n\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}