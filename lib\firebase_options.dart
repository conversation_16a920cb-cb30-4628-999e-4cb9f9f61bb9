// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCdN5ZI2f3fP_mXhVYDxYafU34LSKrvVCA',
    appId: '1:470142947733:web:08fab2a76705c5f725669c',
    messagingSenderId: '470142947733',
    projectId: 'test2-b72c0',
    authDomain: 'test2-b72c0.firebaseapp.com',
    storageBucket: 'test2-b72c0.firebasestorage.app',
    measurementId: 'G-CRDWTH9PFP',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA2zjircaR48u3Yjcv8sKy11JhAdcN2yiE',
    appId: '1:470142947733:android:16919cd344b1091025669c',
    messagingSenderId: '470142947733',
    projectId: 'test2-b72c0',
    storageBucket: 'test2-b72c0.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDvfzMbyN0Nae4zTy-hw3chXcd2mdd6MKM',
    appId: '1:470142947733:ios:68680b0c28d4a2a325669c',
    messagingSenderId: '470142947733',
    projectId: 'test2-b72c0',
    storageBucket: 'test2-b72c0.firebasestorage.app',
    iosBundleId: 'com.example.bttl',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDvfzMbyN0Nae4zTy-hw3chXcd2mdd6MKM',
    appId: '1:470142947733:ios:68680b0c28d4a2a325669c',
    messagingSenderId: '470142947733',
    projectId: 'test2-b72c0',
    storageBucket: 'test2-b72c0.firebasestorage.app',
    iosBundleId: 'com.example.bttl',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCdN5ZI2f3fP_mXhVYDxYafU34LSKrvVCA',
    appId: '1:470142947733:web:be984dd4ffb9948d25669c',
    messagingSenderId: '470142947733',
    projectId: 'test2-b72c0',
    authDomain: 'test2-b72c0.firebaseapp.com',
    storageBucket: 'test2-b72c0.firebasestorage.app',
    measurementId: 'G-58ZPPP0RL0',
  );
}
